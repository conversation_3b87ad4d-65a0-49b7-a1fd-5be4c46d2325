<?php
if (!defined('ABSPATH')) {
    exit;
}

class Q_PWA_Manifest
{
    public static function init()
    {
        add_action('init', [self::class, 'add_manifest_rewrite_rule']);
        add_action('init', [self::class, 'serve_manifest']);
        add_action('template_redirect', [self::class, 'serve_manifest']);
        add_filter('query_vars', [self::class, 'add_manifest_query_var']);

        // Also handle direct requests
        add_action('wp', [self::class, 'handle_manifest_request']);
    }

    public static function add_manifest_rewrite_rule()
    {
        add_rewrite_rule('^manifest\.json$', 'index.php?q_manifest=1', 'top');
    }

    public static function add_manifest_query_var($vars)
    {
        $vars[] = 'q_manifest';
        return $vars;
    }

    public static function serve_manifest()
    {
        if (get_query_var('q_manifest')) {
            self::output_manifest();
        }
    }

    public static function handle_manifest_request()
    {
        // Check if this is a manifest.json request
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($request_uri, '/manifest.json') !== false) {
            self::output_manifest();
        }
    }

    private static function output_manifest()
    {
        // Set headers
        header('Content-Type: application/json');
        header('Cache-Control: public, max-age=3600'); // Cache for 1 hour
        header('Access-Control-Allow-Origin: *');

        // Generate and output manifest
        $manifest = self::generate_manifest();
        echo json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        exit;
    }

    public static function generate_manifest()
    {
        if (!get_option('q_pwa_enabled', false)) {
            return [
                'name' => get_bloginfo('name'),
                'short_name' => get_bloginfo('name'),
                'description' => get_bloginfo('description'),
                'start_url' => '/',
                'display' => 'browser',
                'theme_color' => '#000000',
                'background_color' => '#ffffff',
                'icons' => []
            ];
        }

        $manifest = [
            'name' => get_option('q_pwa_app_name', get_bloginfo('name')),
            'short_name' => get_option('q_pwa_app_short_name', get_bloginfo('name')),
            'description' => get_option('q_pwa_app_description', get_bloginfo('description')),
            'start_url' => get_option('q_pwa_start_url', '/'),
            'display' => get_option('q_pwa_display_mode', 'standalone'),
            'orientation' => get_option('q_pwa_orientation', 'any'),
            'theme_color' => get_option('q_pwa_theme_color', '#000000'),
            'background_color' => get_option('q_pwa_background_color', '#ffffff'),
            'scope' => '/',
            'lang' => get_locale(),
            'dir' => is_rtl() ? 'rtl' : 'ltr',
            'categories' => ['productivity', 'utilities'],
            'prefer_related_applications' => false,
            'icons' => self::generate_icons()
        ];

        // Add optional fields if they exist
        $icon_192 = get_option('q_pwa_icon_192', '');
        $icon_512 = get_option('q_pwa_icon_512', '');

        if ($icon_192 || $icon_512) {
            $manifest['screenshots'] = self::generate_screenshots();
        }

        return apply_filters('q_pwa_manifest', $manifest);
    }

    public static function generate_icons()
    {
        $icons = [];

        // Get custom icons
        $icon_192 = get_option('q_pwa_icon_192', '');
        $icon_512 = get_option('q_pwa_icon_512', '');

        if ($icon_192) {
            $icons[] = [
                'src' => $icon_192,
                'sizes' => '192x192',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ];
        }

        if ($icon_512) {
            $icons[] = [
                'src' => $icon_512,
                'sizes' => '512x512',
                'type' => 'image/png',
                'purpose' => 'any maskable'
            ];
        }

        // Add iOS-specific icons to manifest for better compatibility
        $ios_icons = [
            '57' => get_option('q_pwa_ios_icon_57', ''),
            '72' => get_option('q_pwa_ios_icon_72', ''),
            '76' => get_option('q_pwa_ios_icon_76', ''),
            '114' => get_option('q_pwa_ios_icon_114', ''),
            '120' => get_option('q_pwa_ios_icon_120', ''),
            '144' => get_option('q_pwa_ios_icon_144', ''),
            '152' => get_option('q_pwa_ios_icon_152', ''),
            '180' => get_option('q_pwa_ios_icon_180', '')
        ];

        foreach ($ios_icons as $size => $icon_url) {
            if (!empty($icon_url)) {
                $icons[] = [
                    'src' => $icon_url,
                    'sizes' => $size . 'x' . $size,
                    'type' => 'image/png',
                    'purpose' => 'any'
                ];
            }
        }

        // Fallback to site icon if no custom icons
        if (empty($icons)) {
            $site_icon_id = get_option('site_icon');
            if ($site_icon_id) {
                $site_icon_url = wp_get_attachment_image_url($site_icon_id, 'full');
                if ($site_icon_url) {
                    $icons[] = [
                        'src' => $site_icon_url,
                        'sizes' => '512x512',
                        'type' => 'image/png',
                        'purpose' => 'any'
                    ];
                }
            }
        }

        // Generate additional sizes if we have a base icon
        if (!empty($icons) && $icon_512) {
            $additional_sizes = [
                '72x72',
                '96x96',
                '128x128',
                '144x144',
                '152x152',
                '180x180',
                '192x192',
                '384x384',
                '512x512'
            ];

            foreach ($additional_sizes as $size) {
                if (!self::icon_size_exists($icons, $size)) {
                    $icons[] = [
                        'src' => $icon_512, // Use the 512x512 as fallback
                        'sizes' => $size,
                        'type' => 'image/png',
                        'purpose' => 'any'
                    ];
                }
            }
        }

        return $icons;
    }

    private static function icon_size_exists($icons, $size)
    {
        foreach ($icons as $icon) {
            if ($icon['sizes'] === $size) {
                return true;
            }
        }
        return false;
    }

    public static function generate_screenshots()
    {
        $screenshots = [];

        // You can add logic here to generate screenshots
        // For now, we'll use the app icons as placeholders
        $icon_512 = get_option('q_pwa_icon_512', '');

        if ($icon_512) {
            $screenshots[] = [
                'src' => $icon_512,
                'sizes' => '1280x720',
                'type' => 'image/png',
                'form_factor' => 'wide',
                'label' => 'App Screenshot'
            ];

            $screenshots[] = [
                'src' => $icon_512,
                'sizes' => '750x1334',
                'type' => 'image/png',
                'form_factor' => 'narrow',
                'label' => 'Mobile Screenshot'
            ];
        }

        return $screenshots;
    }

    public static function validate_manifest()
    {
        $manifest = self::generate_manifest();
        $errors = [];

        // Check required fields
        $required_fields = ['name', 'short_name', 'start_url', 'display'];
        foreach ($required_fields as $field) {
            if (empty($manifest[$field])) {
                $errors[] = "Missing required field: {$field}";
            }
        }

        // Validate name length
        if (strlen($manifest['name']) > 45) {
            $errors[] = "App name should be 45 characters or less";
        }

        // Validate short_name length
        if (strlen($manifest['short_name']) > 12) {
            $errors[] = "Short name should be 12 characters or less";
        }

        // Validate start_url
        if (!filter_var($manifest['start_url'], FILTER_VALIDATE_URL) && !preg_match('/^\//', $manifest['start_url'])) {
            $errors[] = "Start URL must be a valid URL or start with /";
        }

        // Validate display mode
        $valid_display_modes = ['fullscreen', 'standalone', 'minimal-ui', 'browser'];
        if (!in_array($manifest['display'], $valid_display_modes)) {
            $errors[] = "Invalid display mode";
        }

        // Validate theme color
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $manifest['theme_color'])) {
            $errors[] = "Theme color must be a valid hex color";
        }

        // Validate background color
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $manifest['background_color'])) {
            $errors[] = "Background color must be a valid hex color";
        }

        // Check for icons
        if (empty($manifest['icons'])) {
            $errors[] = "At least one icon is required";
        } else {
            // Validate icon sizes
            $has_192 = false;
            $has_512 = false;

            foreach ($manifest['icons'] as $icon) {
                if (strpos($icon['sizes'], '192x192') !== false) {
                    $has_192 = true;
                }
                if (strpos($icon['sizes'], '512x512') !== false) {
                    $has_512 = true;
                }
            }

            if (!$has_192) {
                $errors[] = "192x192 icon is recommended";
            }
            if (!$has_512) {
                $errors[] = "512x512 icon is recommended";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'manifest' => $manifest
        ];
    }

    public static function get_manifest_url()
    {
        return home_url('/manifest.json');
    }

    public static function flush_manifest_cache()
    {
        // Clear any caching if needed
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // Flush rewrite rules to ensure manifest route works
        flush_rewrite_rules();
    }

    public static function install_manifest_route()
    {
        self::add_manifest_rewrite_rule();
        flush_rewrite_rules();
    }

    public static function uninstall_manifest_route()
    {
        // Remove rewrite rules on deactivation
        flush_rewrite_rules();
    }
}
