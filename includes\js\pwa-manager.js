/**
 * PWA Manager - Handles Progressive Web App functionality
 */

class QPWAManager {
    constructor() {
        this.settings = window.qPWASettings || {};
        this.deferredPrompt = null;
        this.isInstalled = false;
        
        this.init();
    }

    init() {
        if (!this.settings.enabled) {
            console.log('Q-PWA: PWA functionality is disabled');
            return;
        }

        console.log('Q-PWA: Initializing PWA Manager');
        
        this.checkInstallation();
        this.setupInstallPrompt();
        this.setupServiceWorker();
        this.setupOfflineHandling();
        this.setupAppShell();
        this.addInstallButton();
    }

    checkInstallation() {
        // Check if app is already installed
        if (window.matchMedia('(display-mode: standalone)').matches) {
            this.isInstalled = true;
            console.log('Q-PWA: App is running in standalone mode');
            document.body.classList.add('pwa-installed');
        }

        // Check for iOS standalone mode
        if (window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('Q-PWA: App is running in iOS standalone mode');
            document.body.classList.add('pwa-installed');
        }
    }

    setupInstallPrompt() {
        // Listen for the beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('Q-PWA: Install prompt available');
            
            // Prevent the mini-infobar from appearing on mobile
            e.preventDefault();
            
            // Save the event so it can be triggered later
            this.deferredPrompt = e;
            
            // Show custom install button
            this.showInstallButton();
        });

        // Listen for app installation
        window.addEventListener('appinstalled', (e) => {
            console.log('Q-PWA: App was installed');
            this.isInstalled = true;
            this.hideInstallButton();
            this.deferredPrompt = null;
            
            // Track installation
            this.trackEvent('pwa_installed');
        });
    }

    setupServiceWorker() {
        if (!('serviceWorker' in navigator)) {
            console.log('Q-PWA: Service workers not supported');
            return;
        }

        // Register service worker
        navigator.serviceWorker.register('/firebase-messaging-sw.js', {
            scope: '/'
        })
        .then((registration) => {
            console.log('Q-PWA: Service worker registered successfully');
            
            // Listen for service worker updates
            registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                
                newWorker.addEventListener('statechange', () => {
                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                        // New content is available
                        this.showUpdateNotification();
                    }
                });
            });
        })
        .catch((error) => {
            console.error('Q-PWA: Service worker registration failed:', error);
        });

        // Listen for service worker messages
        navigator.serviceWorker.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'NOTIFICATION_CLICKED') {
                console.log('Q-PWA: Notification clicked', event.data);
                this.handleNotificationClick(event.data);
            }
        });
    }

    setupOfflineHandling() {
        if (!this.settings.offlineEnabled) {
            return;
        }

        // Initialize connection monitoring
        this.connectionQuality = 'unknown';
        this.lastConnectionCheck = Date.now();
        this.connectionCheckInterval = null;

        // Listen for online/offline events
        window.addEventListener('online', () => {
            console.log('Q-PWA: Back online');
            this.handleOnlineStatus(true);
        });

        window.addEventListener('offline', () => {
            console.log('Q-PWA: Gone offline');
            this.handleOnlineStatus(false);
        });

        // Monitor connection quality
        this.startConnectionMonitoring();

        // Check initial connection status
        if (!navigator.onLine) {
            this.showOfflineNotification();
        } else {
            this.checkConnectionQuality();
        }

        // Setup keyboard event listeners for accessibility
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideOfflineNotification();
            }
        });
    }

    handleOnlineStatus(isOnline) {
        if (isOnline) {
            this.showOnlineNotification();
            setTimeout(() => {
                this.hideOfflineNotification();
            }, 2000); // Show "back online" message briefly
            this.syncOfflineData();
            this.checkConnectionQuality();
        } else {
            this.showOfflineNotification();
        }
    }

    startConnectionMonitoring() {
        // Only start monitoring if enabled
        if (!this.settings.connectionMonitoringEnabled) {
            return;
        }

        // Check connection quality every 30 seconds when online
        this.connectionCheckInterval = setInterval(() => {
            if (navigator.onLine) {
                this.checkConnectionQuality();
            }
        }, 30000);
    }

    async checkConnectionQuality() {
        if (!navigator.onLine) {
            this.connectionQuality = 'offline';
            return;
        }

        try {
            const startTime = Date.now();
            const response = await fetch(this.settings.siteUrl + '?connection-test=' + Date.now(), {
                method: 'HEAD',
                cache: 'no-cache'
            });
            const endTime = Date.now();
            const responseTime = endTime - startTime;

            if (response.ok) {
                if (responseTime < 1000) {
                    this.connectionQuality = 'good';
                } else if (responseTime < 3000) {
                    this.connectionQuality = 'fair';
                    this.showSlowConnectionNotification();
                } else {
                    this.connectionQuality = 'poor';
                    this.showSlowConnectionNotification();
                }
            } else {
                this.connectionQuality = 'poor';
                this.showSlowConnectionNotification();
            }
        } catch (error) {
            this.connectionQuality = 'poor';
            console.log('Q-PWA: Connection quality check failed:', error);
        }
    }

    setupAppShell() {
        // Add PWA-specific classes to body
        document.body.classList.add('pwa-enabled');

        // Add viewport meta tag if not present
        if (!document.querySelector('meta[name="viewport"]')) {
            const viewport = document.createElement('meta');
            viewport.name = 'viewport';
            viewport.content = 'width=device-width, initial-scale=1, shrink-to-fit=no';
            document.head.appendChild(viewport);
        }

        // iOS-specific enhancements
        this.setupiOSEnhancements();

        // Handle iOS status bar
        if (this.isInstalled && window.navigator.standalone) {
            document.body.classList.add('ios-standalone');
        }
    }

    setupiOSEnhancements() {
        // Detect iOS
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

        if (isIOS) {
            document.body.classList.add('ios-device');

            // Prevent zoom on iOS
            document.addEventListener('gesturestart', (e) => {
                e.preventDefault();
            });

            // Prevent double-tap zoom
            let lastTouchEnd = 0;
            document.addEventListener('touchend', (e) => {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    e.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // Handle iOS safe areas
            this.handleiOSSafeAreas();

            // iOS-specific PWA detection
            if (window.navigator.standalone) {
                document.body.classList.add('ios-pwa-mode');
                console.log('Q-PWA: Running in iOS standalone mode');
            }

            // iOS installation prompt handling
            this.setupiOSInstallPrompt();
        }
    }

    handleiOSSafeAreas() {
        // Add CSS custom properties for iOS safe areas
        const style = document.createElement('style');
        style.textContent = `
            :root {
                --safe-area-inset-top: env(safe-area-inset-top);
                --safe-area-inset-right: env(safe-area-inset-right);
                --safe-area-inset-bottom: env(safe-area-inset-bottom);
                --safe-area-inset-left: env(safe-area-inset-left);
            }

            .ios-pwa-mode {
                padding-top: var(--safe-area-inset-top);
                padding-bottom: var(--safe-area-inset-bottom);
            }
        `;
        document.head.appendChild(style);
    }

    setupiOSInstallPrompt() {
        // iOS doesn't support the beforeinstallprompt event
        // Instead, we can show custom instructions
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        const isInStandaloneMode = window.navigator.standalone;

        if (isIOS && !isInStandaloneMode) {
            // Show iOS-specific install instructions after a delay
            setTimeout(() => {
                this.showiOSInstallInstructions();
            }, 10000); // Show after 10 seconds
        }
    }

    showiOSInstallInstructions() {
        // Only show if user hasn't dismissed it recently
        const dismissed = localStorage.getItem('q-pwa-ios-install-dismissed');
        const dismissedTime = dismissed ? parseInt(dismissed) : 0;
        const dayInMs = 24 * 60 * 60 * 1000;

        if (Date.now() - dismissedTime < dayInMs) {
            return; // Don't show if dismissed within last 24 hours
        }

        const instructionsHtml = `
            <div id="q-ios-install-prompt" style="
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: #007cba;
                color: white;
                padding: 15px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                line-height: 1.4;
            ">
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div>
                        <strong>📱 Install this app</strong><br>
                        Tap <span style="font-weight: bold;">Share</span> then <span style="font-weight: bold;">"Add to Home Screen"</span>
                    </div>
                    <button id="q-ios-install-close" style="
                        background: none;
                        border: none;
                        color: white;
                        font-size: 18px;
                        cursor: pointer;
                        padding: 0;
                        margin-left: 10px;
                    ">×</button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', instructionsHtml);

        // Handle close button
        document.getElementById('q-ios-install-close').addEventListener('click', () => {
            document.getElementById('q-ios-install-prompt').remove();
            localStorage.setItem('q-pwa-ios-install-dismissed', Date.now().toString());
        });

        // Auto-hide after 10 seconds
        setTimeout(() => {
            const prompt = document.getElementById('q-ios-install-prompt');
            if (prompt) {
                prompt.remove();
            }
        }, 10000);
    }

    addInstallButton() {
        // Create install button container
        const installContainer = document.createElement('div');
        installContainer.id = 'q-pwa-install-container';
        installContainer.className = 'q-pwa-install-container hidden';
        
        installContainer.innerHTML = `
            <div class="q-pwa-install-prompt">
                <div class="q-pwa-install-content">
                    <div class="q-pwa-install-icon">📱</div>
                    <div class="q-pwa-install-text">
                        <h3>Install ${this.settings.appName || 'App'}</h3>
                        <p>Get the full app experience with offline access and push notifications.</p>
                    </div>
                    <div class="q-pwa-install-actions">
                        <button id="q-pwa-install-btn" class="q-pwa-btn-primary">Install</button>
                        <button id="q-pwa-install-dismiss" class="q-pwa-btn-secondary">Not now</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(installContainer);

        // Add event listeners
        document.getElementById('q-pwa-install-btn').addEventListener('click', () => {
            this.promptInstall();
        });

        document.getElementById('q-pwa-install-dismiss').addEventListener('click', () => {
            this.hideInstallButton();
            this.trackEvent('install_prompt_dismissed');
        });
    }

    showInstallButton() {
        const container = document.getElementById('q-pwa-install-container');
        if (container && !this.isInstalled) {
            container.classList.remove('hidden');
            this.trackEvent('install_prompt_shown');
        }
    }

    hideInstallButton() {
        const container = document.getElementById('q-pwa-install-container');
        if (container) {
            container.classList.add('hidden');
        }
    }

    promptInstall() {
        if (!this.deferredPrompt) {
            console.log('Q-PWA: No install prompt available');
            return;
        }

        // Show the install prompt
        this.deferredPrompt.prompt();

        // Wait for the user to respond to the prompt
        this.deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('Q-PWA: User accepted the install prompt');
                this.trackEvent('install_accepted');
            } else {
                console.log('Q-PWA: User dismissed the install prompt');
                this.trackEvent('install_dismissed');
            }
            
            this.deferredPrompt = null;
            this.hideInstallButton();
        });
    }

    showUpdateNotification() {
        // Create update notification
        const notification = document.createElement('div');
        notification.id = 'q-pwa-update-notification';
        notification.className = 'q-pwa-notification';
        notification.innerHTML = `
            <div class="q-pwa-notification-content">
                <span>🔄 New version available!</span>
                <button id="q-pwa-update-btn">Update</button>
                <button id="q-pwa-update-dismiss">×</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Add event listeners
        document.getElementById('q-pwa-update-btn').addEventListener('click', () => {
            window.location.reload();
        });

        document.getElementById('q-pwa-update-dismiss').addEventListener('click', () => {
            notification.remove();
        });

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }

    showOfflineNotification() {
        // Check if offline notifications are enabled
        if (!this.settings.offlineNotificationEnabled) {
            return;
        }

        this.hideOfflineNotification(); // Remove any existing notification

        const notification = this.createNotification('offline', {
            icon: this.settings.offlineIcon || '📡',
            title: this.settings.offlineTitle || 'You\'re Offline',
            message: this.settings.offlineMessage || 'Some features may be limited while offline.',
            actions: [
                {
                    text: 'Retry',
                    action: () => this.retryConnection(),
                    primary: true
                },
                {
                    text: '×',
                    action: () => this.hideOfflineNotification(),
                    close: true
                }
            ],
            persistent: true
        });

        document.body.appendChild(notification);
        this.trackEvent('offline_notification_shown');
    }

    showOnlineNotification() {
        this.hideOfflineNotification(); // Remove offline notification

        const notification = this.createNotification('online', {
            icon: '✅',
            title: 'Back Online',
            message: 'Your connection has been restored.',
            autoHide: 3000
        });

        document.body.appendChild(notification);
        this.trackEvent('online_notification_shown');
    }

    showSlowConnectionNotification() {
        // Check if slow connection notifications are enabled
        if (!this.settings.slowConnectionNotification) {
            return;
        }

        // Don't show if offline notification is already visible
        if (document.getElementById('q-pwa-offline-notification')) {
            return;
        }

        // Don't show too frequently
        const lastShown = localStorage.getItem('q_pwa_last_slow_notification');
        if (lastShown && Date.now() - parseInt(lastShown) < 60000) {
            return; // Don't show more than once per minute
        }

        const notification = this.createNotification('slow-connection', {
            icon: '🐌',
            title: 'Slow Connection',
            message: 'Your internet connection seems slow. Some features may take longer to load.',
            actions: [
                {
                    text: '×',
                    action: () => this.hideNotification('q-pwa-slow-connection-notification'),
                    close: true
                }
            ],
            autoHide: 8000
        });

        document.body.appendChild(notification);
        localStorage.setItem('q_pwa_last_slow_notification', Date.now().toString());
        this.trackEvent('slow_connection_notification_shown');
    }

    createNotification(type, options) {
        const notification = document.createElement('div');
        notification.id = `q-pwa-${type}-notification`;
        notification.className = `q-pwa-notification ${type}`;
        notification.setAttribute('role', 'alert');
        notification.setAttribute('aria-live', 'polite');

        let actionsHtml = '';
        if (options.actions && options.actions.length > 0) {
            actionsHtml = '<div class="q-pwa-notification-actions">';
            options.actions.forEach(action => {
                const buttonClass = action.primary ? 'primary' : (action.close ? 'close' : '');
                actionsHtml += `<button class="q-pwa-notification-btn ${buttonClass}" data-action="${action.text}">${action.text}</button>`;
            });
            actionsHtml += '</div>';
        }

        notification.innerHTML = `
            <div class="q-pwa-notification-content">
                <div class="q-pwa-notification-main">
                    <span class="q-pwa-notification-icon">${options.icon}</span>
                    <div class="q-pwa-notification-text">
                        <div class="q-pwa-notification-title">${options.title}</div>
                        <div class="q-pwa-notification-message">${options.message}</div>
                    </div>
                </div>
                ${actionsHtml}
            </div>
            ${options.autoHide ? `<div class="q-pwa-notification-progress"></div>` : ''}
        `;

        // Add event listeners for actions
        if (options.actions) {
            options.actions.forEach(action => {
                const button = notification.querySelector(`[data-action="${action.text}"]`);
                if (button && action.action) {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        action.action();
                    });
                }
            });
        }

        // Add click-to-dismiss functionality
        if (!options.persistent) {
            notification.addEventListener('click', () => {
                this.hideNotification(notification.id);
            });
        }

        // Auto-hide functionality
        if (options.autoHide) {
            const progressBar = notification.querySelector('.q-pwa-notification-progress');
            if (progressBar) {
                progressBar.style.animationDuration = `${options.autoHide}ms`;
            }

            setTimeout(() => {
                this.hideNotification(notification.id);
            }, options.autoHide);
        }

        return notification;
    }

    hideOfflineNotification() {
        this.hideNotification('q-pwa-offline-notification');
    }

    hideNotification(notificationId) {
        const notification = document.getElementById(notificationId);
        if (notification) {
            notification.classList.add('hiding');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300); // Match CSS animation duration
        }
    }

    async retryConnection() {
        const notification = document.getElementById('q-pwa-offline-notification');
        if (notification) {
            const retryBtn = notification.querySelector('.q-pwa-notification-btn.primary');
            if (retryBtn) {
                retryBtn.textContent = 'Checking...';
                retryBtn.disabled = true;
            }
        }

        try {
            const response = await fetch(this.settings.siteUrl + '?connection-test=' + Date.now(), {
                method: 'HEAD',
                cache: 'no-cache'
            });

            if (response.ok) {
                this.showOnlineNotification();
                this.syncOfflineData();
            } else {
                throw new Error('Connection failed');
            }
        } catch (error) {
            // Reset retry button
            if (notification) {
                const retryBtn = notification.querySelector('.q-pwa-notification-btn.primary');
                if (retryBtn) {
                    retryBtn.textContent = 'Retry';
                    retryBtn.disabled = false;
                }
            }
            console.log('Q-PWA: Retry connection failed:', error);
        }

        this.trackEvent('connection_retry_attempted');
    }

    syncOfflineData() {
        // Implement offline data synchronization
        console.log('Q-PWA: Syncing offline data...');

        // Sync offline events
        this.syncOfflineEvents();

        // This would typically sync any cached form submissions,
        // user actions, or other data that was stored while offline
    }

    syncOfflineEvents() {
        try {
            const offlineEvents = JSON.parse(localStorage.getItem('q_pwa_offline_events') || '[]');

            if (offlineEvents.length === 0) {
                return;
            }

            console.log(`Q-PWA: Syncing ${offlineEvents.length} offline events`);

            // Send each event
            offlineEvents.forEach((eventData, index) => {
                if (this.settings.ajaxUrl && this.settings.nonce) {
                    jQuery.post(this.settings.ajaxUrl, {
                        action: 'q_track_pwa_event',
                        event: eventData.event,
                        data: eventData.data,
                        timestamp: eventData.timestamp,
                        nonce: this.settings.nonce
                    }).done(() => {
                        console.log('Q-PWA: Offline event synced:', eventData.event);
                    }).fail((xhr, status, error) => {
                        console.error('Q-PWA: Failed to sync offline event:', error);
                    });
                }
            });

            // Clear synced events
            localStorage.removeItem('q_pwa_offline_events');
            console.log('Q-PWA: Offline events cleared from storage');

        } catch (error) {
            console.error('Q-PWA: Failed to sync offline events:', error);
        }
    }

    handleNotificationClick(data) {
        // Handle notification click events
        console.log('Q-PWA: Handling notification click', data);
        
        // You can implement custom logic here based on the notification data
        if (data.notification && data.notification.id) {
            this.trackEvent('notification_clicked', {
                notification_id: data.notification.id
            });
        }
    }

    trackEvent(eventName, data = {}) {
        // Track PWA events for analytics
        console.log('Q-PWA Event:', eventName, data);

        // Send to analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, {
                event_category: 'PWA',
                ...data
            });
        }

        // Send to WordPress via AJAX only if online
        if (this.settings.ajaxUrl && this.settings.nonce && navigator.onLine) {
            jQuery.post(this.settings.ajaxUrl, {
                action: 'q_track_pwa_event',
                event: eventName,
                data: data,
                nonce: this.settings.nonce
            }).fail((xhr, status, error) => {
                console.log('Q-PWA: Failed to track event:', error);
                // Store event for later sync when back online
                this.storeOfflineEvent(eventName, data);
            });
        } else if (!navigator.onLine) {
            // Store event for later sync when back online
            this.storeOfflineEvent(eventName, data);
        }
    }

    storeOfflineEvent(eventName, data) {
        // Store events in localStorage for sync when back online
        try {
            const offlineEvents = JSON.parse(localStorage.getItem('q_pwa_offline_events') || '[]');
            offlineEvents.push({
                event: eventName,
                data: data,
                timestamp: Date.now()
            });
            localStorage.setItem('q_pwa_offline_events', JSON.stringify(offlineEvents));
            console.log('Q-PWA: Event stored for offline sync:', eventName);
        } catch (error) {
            console.error('Q-PWA: Failed to store offline event:', error);
        }
    }
}

// Initialize PWA Manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.qPWAManager = new QPWAManager();
});

// Export for use in other scripts
window.QPWAManager = QPWAManager;
